"""
Routes pour la gestion des évaluations de performance des employés
"""
from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.modules.employees.models import Employee, Performance
from app.modules.employees.forms import PerformanceForm
from app import db
from app.utils.decorators import permission_required
from datetime import date, datetime
from . import bp

@bp.route('/<int:id>/performance')
@login_required
@permission_required('can_manage_employees')
def performance(id):
    """Liste des évaluations de performance d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    page = request.args.get('page', 1, type=int)
    performances = Performance.query.filter_by(employee_id=id).order_by(
        Performance.evaluation_date.desc()
    ).paginate(page=page, per_page=10, error_out=False)
    
    # Calculer la moyenne des scores
    all_performances = Performance.query.filter_by(employee_id=id).all()
    average_score = None
    if all_performances:
        total_score = sum(p.overall_score for p in all_performances if p.overall_score)
        average_score = total_score / len(all_performances) if all_performances else 0
    
    return render_template('employees/performance.html', 
                         employee=employee, 
                         performances=performances,
                         average_score=average_score)

@bp.route('/<int:id>/performance/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new_performance(id):
    """Créer une nouvelle évaluation de performance"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    form = PerformanceForm()
    
    # Pré-remplir les dates par défaut
    if request.method == 'GET':
        today = date.today()
        # Période d'évaluation des 6 derniers mois
        form.evaluation_period_end.data = today
        form.evaluation_period_start.data = date(today.year, max(1, today.month - 6), 1)
        form.evaluation_date.data = today
    
    if form.validate_on_submit():
        performance = Performance(
            employee_id=employee.id,
            owner_id=current_user.get_owner_id,
            evaluator_id=current_user.id,
            evaluation_period_start=form.evaluation_period_start.data,
            evaluation_period_end=form.evaluation_period_end.data,
            evaluation_date=form.evaluation_date.data,
            punctuality_score=form.punctuality_score.data,
            quality_of_work_score=form.quality_of_work_score.data,
            teamwork_score=form.teamwork_score.data,
            communication_score=form.communication_score.data,
            initiative_score=form.initiative_score.data,
            customer_service_score=form.customer_service_score.data,
            strengths=form.strengths.data,
            areas_for_improvement=form.areas_for_improvement.data,
            goals_next_period=form.goals_next_period.data,
            evaluator_comments=form.evaluator_comments.data,
            employee_comments=form.employee_comments.data,
            is_finalized=form.is_finalized.data
        )
        
        # Calculer le score global
        performance.calculate_overall_score()
        
        db.session.add(performance)
        db.session.commit()
        
        flash('Évaluation de performance créée avec succès!', 'success')
        return redirect(url_for('employees.performance', id=employee.id))
    
    return render_template('employees/performance_form.html', 
                         form=form, 
                         employee=employee, 
                         title="Nouvelle Évaluation")

@bp.route('/performance/<int:performance_id>')
@login_required
@permission_required('can_manage_employees')
def performance_detail(performance_id):
    """Détails d'une évaluation de performance"""
    performance = Performance.query.get_or_404(performance_id)
    if performance.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    return render_template('employees/performance_detail.html', performance=performance)

@bp.route('/performance/<int:performance_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit_performance(performance_id):
    """Modifier une évaluation de performance"""
    performance = Performance.query.get_or_404(performance_id)
    if performance.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    if performance.is_finalized and performance.employee_acknowledged:
        flash('Cette évaluation a été finalisée et reconnue par l\'employé. Elle ne peut plus être modifiée.', 'error')
        return redirect(url_for('employees.performance_detail', performance_id=performance_id))
    
    form = PerformanceForm(obj=performance)
    
    if form.validate_on_submit():
        form.populate_obj(performance)
        performance.calculate_overall_score()  # Recalculer le score
        
        db.session.commit()
        
        flash('Évaluation mise à jour avec succès!', 'success')
        return redirect(url_for('employees.performance_detail', performance_id=performance_id))
    
    return render_template('employees/performance_form.html', 
                         form=form, 
                         employee=performance.employee, 
                         performance=performance,
                         title="Modifier Évaluation")

@bp.route('/performance/<int:performance_id>/acknowledge', methods=['POST'])
@login_required
def acknowledge_performance(performance_id):
    """Permettre à un employé de reconnaître son évaluation"""
    performance = Performance.query.get_or_404(performance_id)
    
    # Vérifier que l'utilisateur actuel est l'employé évalué ou a les permissions
    if (current_user.employee_profile and 
        current_user.employee_profile.id == performance.employee_id) or \
       current_user.has_permission('can_manage_employees'):
        
        performance.employee_acknowledged = True
        performance.acknowledged_at = datetime.utcnow()
        
        db.session.commit()
        
        flash('Évaluation reconnue avec succès.', 'success')
    else:
        flash('Accès non autorisé', 'error')
    
    return redirect(url_for('employees.performance_detail', performance_id=performance_id))

@bp.route('/performance/<int:performance_id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def delete_performance(performance_id):
    """Supprimer une évaluation de performance"""
    performance = Performance.query.get_or_404(performance_id)
    if performance.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    if performance.is_finalized and performance.employee_acknowledged:
        flash('Cette évaluation a été finalisée et reconnue. Elle ne peut pas être supprimée.', 'error')
        return redirect(url_for('employees.performance', id=performance.employee_id))
    
    employee_id = performance.employee_id
    db.session.delete(performance)
    db.session.commit()
    
    flash('Évaluation supprimée avec succès!', 'success')
    return redirect(url_for('employees.performance', id=employee_id))

# Routes pour les rapports de performance
@bp.route('/performance/reports')
@login_required
@permission_required('can_view_employee_reports')
def performance_reports():
    """Rapports de performance globaux"""
    owner_id = current_user.get_owner_id
    
    # Statistiques générales
    total_evaluations = Performance.query.filter_by(owner_id=owner_id).count()
    finalized_evaluations = Performance.query.filter_by(owner_id=owner_id, is_finalized=True).count()
    acknowledged_evaluations = Performance.query.filter_by(owner_id=owner_id, employee_acknowledged=True).count()
    
    # Moyennes par critère
    all_performances = Performance.query.filter_by(owner_id=owner_id, is_finalized=True).all()
    
    if all_performances:
        avg_punctuality = sum(p.punctuality_score for p in all_performances) / len(all_performances)
        avg_quality = sum(p.quality_of_work_score for p in all_performances) / len(all_performances)
        avg_teamwork = sum(p.teamwork_score for p in all_performances) / len(all_performances)
        avg_communication = sum(p.communication_score for p in all_performances) / len(all_performances)
        avg_initiative = sum(p.initiative_score for p in all_performances) / len(all_performances)
        avg_customer_service = sum(p.customer_service_score for p in all_performances) / len(all_performances)
        avg_overall = sum(p.overall_score for p in all_performances if p.overall_score) / len(all_performances)
    else:
        avg_punctuality = avg_quality = avg_teamwork = avg_communication = 0
        avg_initiative = avg_customer_service = avg_overall = 0
    
    # Top performers
    top_performers = sorted(all_performances, key=lambda p: p.overall_score or 0, reverse=True)[:5]
    
    # Employés nécessitant une attention
    needs_attention = [p for p in all_performances if (p.overall_score or 0) < 2.5]
    
    return render_template('employees/performance_reports.html',
                         total_evaluations=total_evaluations,
                         finalized_evaluations=finalized_evaluations,
                         acknowledged_evaluations=acknowledged_evaluations,
                         avg_punctuality=avg_punctuality,
                         avg_quality=avg_quality,
                         avg_teamwork=avg_teamwork,
                         avg_communication=avg_communication,
                         avg_initiative=avg_initiative,
                         avg_customer_service=avg_customer_service,
                         avg_overall=avg_overall,
                         top_performers=top_performers,
                         needs_attention=needs_attention)

@bp.route('/<int:id>/performance/history')
@login_required
@permission_required('can_manage_employees')
def performance_history(id):
    """Historique des performances d'un employé avec graphiques"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))
    
    performances = Performance.query.filter_by(
        employee_id=id, 
        is_finalized=True
    ).order_by(Performance.evaluation_date).all()
    
    # Préparer les données pour les graphiques
    performance_data = []
    for perf in performances:
        performance_data.append({
            'date': perf.evaluation_date.strftime('%Y-%m-%d'),
            'overall_score': float(perf.overall_score) if perf.overall_score else 0,
            'punctuality': perf.punctuality_score,
            'quality': perf.quality_of_work_score,
            'teamwork': perf.teamwork_score,
            'communication': perf.communication_score,
            'initiative': perf.initiative_score,
            'customer_service': perf.customer_service_score
        })
    
    return render_template('employees/performance_history.html', 
                         employee=employee,
                         performances=performances,
                         performance_data=performance_data)
