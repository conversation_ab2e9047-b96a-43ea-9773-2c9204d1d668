{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-user-edit me-2"></i>{{ title }}
                </h1>
                <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-body">
                            <form method="POST" novalidate>
                                {{ form.hidden_tag() }}
                                
                                <!-- Informations personnelles -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-user me-2"></i>Informations Personnelles
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                {{ form.employee_id.label(class="form-label") }}
                                                {{ form.employee_id(class="form-control" + (" is-invalid" if form.employee_id.errors else "")) }}
                                                {% if form.employee_id.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.employee_id.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                {{ form.first_name.label(class="form-label") }}
                                                {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                                {% if form.first_name.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                {{ form.last_name.label(class="form-label") }}
                                                {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                                {% if form.last_name.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                {{ form.email.label(class="form-label") }}
                                                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                                {% if form.email.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.email.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                {{ form.phone.label(class="form-label") }}
                                                {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                                {% if form.phone.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                {{ form.date_of_birth.label(class="form-label") }}
                                                {{ form.date_of_birth(class="form-control" + (" is-invalid" if form.date_of_birth.errors else "")) }}
                                                {% if form.date_of_birth.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.date_of_birth.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                {{ form.national_id.label(class="form-label") }}
                                                {{ form.national_id(class="form-control" + (" is-invalid" if form.national_id.errors else "")) }}
                                                {% if form.national_id.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.national_id.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            {{ form.address.label(class="form-label") }}
                                            {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows="3") }}
                                            {% if form.address.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.address.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations d'emploi -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-briefcase me-2"></i>Informations d'Emploi
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                {{ form.hire_date.label(class="form-label") }}
                                                {{ form.hire_date(class="form-control" + (" is-invalid" if form.hire_date.errors else "")) }}
                                                {% if form.hire_date.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.hire_date.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                {{ form.status.label(class="form-label") }}
                                                {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                                                {% if form.status.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.status.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                {{ form.contract_type.label(class="form-label") }}
                                                {{ form.contract_type(class="form-select" + (" is-invalid" if form.contract_type.errors else "")) }}
                                                {% if form.contract_type.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.contract_type.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                {{ form.department.label(class="form-label") }}
                                                {{ form.department(class="form-control" + (" is-invalid" if form.department.errors else "")) }}
                                                {% if form.department.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.department.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                {{ form.position.label(class="form-label") }}
                                                {{ form.position(class="form-control" + (" is-invalid" if form.position.errors else "")) }}
                                                {% if form.position.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.position.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        {% if form.status.data == 'TERMINATED' or (employee and employee.status == 'TERMINATED') %}
                                        <div class="mb-3">
                                            {{ form.termination_date.label(class="form-label") }}
                                            {{ form.termination_date(class="form-control" + (" is-invalid" if form.termination_date.errors else "")) }}
                                            {% if form.termination_date.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.termination_date.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Informations salariales -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-money-bill-wave me-2"></i>Informations Salariales
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4 mb-3">
                                                {{ form.base_salary.label(class="form-label") }}
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    {{ form.base_salary(class="form-control" + (" is-invalid" if form.base_salary.errors else "")) }}
                                                </div>
                                                {% if form.base_salary.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.base_salary.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                {{ form.hourly_rate.label(class="form-label") }}
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    {{ form.hourly_rate(class="form-control" + (" is-invalid" if form.hourly_rate.errors else "")) }}
                                                    <span class="input-group-text">/h</span>
                                                </div>
                                                {% if form.hourly_rate.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.hourly_rate.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="col-md-4 mb-3">
                                                {{ form.payment_frequency.label(class="form-label") }}
                                                {{ form.payment_frequency(class="form-select" + (" is-invalid" if form.payment_frequency.errors else "")) }}
                                                {% if form.payment_frequency.errors %}
                                                    <div class="invalid-feedback">
                                                        {% for error in form.payment_frequency.errors %}{{ error }}{% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>{{ form.submit.label.text }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Afficher/masquer le champ date de fin selon le statut
document.getElementById('status').addEventListener('change', function() {
    const terminationDateField = document.querySelector('[name="termination_date"]').closest('.mb-3');
    if (this.value === 'TERMINATED') {
        terminationDateField.style.display = 'block';
    } else {
        terminationDateField.style.display = 'none';
    }
});

// Validation côté client
document.querySelector('form').addEventListener('submit', function(e) {
    const basesalary = parseFloat(document.getElementById('base_salary').value);
    const hourlyRate = parseFloat(document.getElementById('hourly_rate').value);
    
    if (!basesalary && !hourlyRate) {
        e.preventDefault();
        alert('Veuillez saisir soit un salaire de base, soit un taux horaire.');
        return false;
    }
});
</script>
{% endblock %}
