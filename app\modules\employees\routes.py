from flask import render_template, redirect, url_for, flash, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app.modules.employees.models import (
    Employee, EmployeeProfile, WorkSchedule, Attendance, Payroll, Performance, EmployeeDocument,
    EmployeeStatus, AttendanceStatus
)
from app.modules.employees.forms import (
    EmployeeForm, EmployeeProfileForm, WorkScheduleForm, AttendanceForm,
    PayrollForm, PerformanceForm, EmployeeDocumentForm, EmployeeSearchForm
)
from app.modules.auth.models import User
from app import db
from app.utils.decorators import permission_required
from datetime import date, datetime, timedelta
import os
from . import bp

@bp.route('/test')
def test():
    """Route de test pour vérifier que le module fonctionne"""
    return render_template('employees/test.html')

@bp.route('/')
@login_required
@permission_required('can_manage_employees')
def index():
    """Liste des employés avec recherche et filtrage"""
    page = request.args.get('page', 1, type=int)
    owner_id = current_user.get_owner_id

    form = EmployeeSearchForm()
    query = Employee.query.filter_by(owner_id=owner_id)

    # Filtrage par recherche
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(
            db.or_(
                Employee.first_name.contains(search_term),
                Employee.last_name.contains(search_term),
                Employee.employee_id.contains(search_term),
                Employee.email.contains(search_term)
            )
        )

    # Filtrage par statut
    if request.args.get('status'):
        query = query.filter_by(status=request.args.get('status'))

    # Filtrage par département
    if request.args.get('department'):
        query = query.filter(Employee.department.contains(request.args.get('department')))

    # Filtrage par poste
    if request.args.get('position'):
        query = query.filter(Employee.position.contains(request.args.get('position')))

    employees = query.order_by(Employee.last_name, Employee.first_name).paginate(
        page=page, per_page=20, error_out=False)

    return render_template('employees/index.html', employees=employees, form=form)

@bp.route('/dashboard')
@login_required
@permission_required('can_manage_employees')
def dashboard():
    """Tableau de bord RH"""
    owner_id = current_user.get_owner_id
    
    # Statistiques générales
    total_employees = Employee.query.filter_by(owner_id=owner_id).count()
    active_employees = Employee.query.filter_by(owner_id=owner_id, status='ACTIVE').count()
    
    # Employés récemment embauchés (30 derniers jours)
    from datetime import date, timedelta
    thirty_days_ago = date.today() - timedelta(days=30)
    recent_hires = Employee.query.filter(
        Employee.owner_id == owner_id,
        Employee.hire_date >= thirty_days_ago
    ).count()
    
    # Présences du jour
    today = date.today()
    today_attendances = Attendance.query.join(Employee).filter(
        Employee.owner_id == owner_id,
        Attendance.date == today
    ).count()
    
    return render_template('employees/dashboard.html', 
                         total_employees=total_employees,
                         active_employees=active_employees,
                         recent_hires=recent_hires,
                         today_attendances=today_attendances)

@bp.route('/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new():
    """Créer un nouvel employé"""
    form = EmployeeForm()

    if form.validate_on_submit():
        employee = Employee(
            employee_id=form.employee_id.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            email=form.email.data,
            phone=form.phone.data,
            address=form.address.data,
            date_of_birth=form.date_of_birth.data,
            national_id=form.national_id.data,
            hire_date=form.hire_date.data,
            termination_date=form.termination_date.data,
            status=form.status.data,
            contract_type=form.contract_type.data,
            department=form.department.data,
            position=form.position.data,
            base_salary=form.base_salary.data,
            hourly_rate=form.hourly_rate.data,
            payment_frequency=form.payment_frequency.data,
            owner_id=current_user.get_owner_id
        )

        db.session.add(employee)
        db.session.commit()

        flash(f'Employé {employee.full_name} créé avec succès!', 'success')
        return redirect(url_for('employees.detail', id=employee.id))

    return render_template('employees/form.html', form=form, title="Nouvel Employé")

@bp.route('/<int:id>')
@login_required
@permission_required('can_manage_employees')
def detail(id):
    """Détails d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Statistiques récentes
    today = date.today()
    current_month = today.replace(day=1)

    # Présences du mois en cours
    monthly_attendances = Attendance.query.filter(
        Attendance.employee_id == id,
        Attendance.date >= current_month,
        Attendance.status == AttendanceStatus.PRESENT
    ).count()

    # Heures travaillées ce mois
    monthly_hours = employee.get_monthly_hours(today.year, today.month)

    # Dernière évaluation
    last_performance = Performance.query.filter_by(employee_id=id).order_by(Performance.evaluation_date.desc()).first()

    return render_template('employees/detail.html',
                         employee=employee,
                         monthly_attendances=monthly_attendances,
                         monthly_hours=monthly_hours,
                         last_performance=last_performance)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit(id):
    """Modifier un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = EmployeeForm(obj=employee)
    form.employee = employee  # Pour la validation d'unicité

    if form.validate_on_submit():
        form.populate_obj(employee)
        db.session.commit()

        flash(f'Employé {employee.full_name} mis à jour avec succès!', 'success')
        return redirect(url_for('employees.detail', id=employee.id))

    return render_template('employees/form.html', form=form, employee=employee, title="Modifier Employé")

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def delete(id):
    """Supprimer un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Marquer comme inactif plutôt que supprimer
    employee.status = EmployeeStatus.TERMINATED
    employee.termination_date = date.today()
    db.session.commit()

    flash(f'Employé {employee.full_name} marqué comme terminé.', 'info')
    return redirect(url_for('employees.index'))

# Routes pour la gestion des profils
@bp.route('/<int:id>/profile', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def profile(id):
    """Gérer le profil détaillé d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    # Créer le profil s'il n'existe pas
    if not employee.profile:
        employee.profile = EmployeeProfile(employee_id=employee.id)
        db.session.add(employee.profile)
        db.session.commit()

    form = EmployeeProfileForm(obj=employee.profile)

    if form.validate_on_submit():
        form.populate_obj(employee.profile)
        db.session.commit()

        flash('Profil mis à jour avec succès!', 'success')
        return redirect(url_for('employees.detail', id=employee.id))

    return render_template('employees/profile.html', form=form, employee=employee)

# Routes pour la gestion des plannings
@bp.route('/<int:id>/schedules')
@login_required
@permission_required('can_manage_employees')
def schedules(id):
    """Liste des plannings d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    page = request.args.get('page', 1, type=int)
    schedules = WorkSchedule.query.filter_by(employee_id=id).order_by(
        WorkSchedule.start_date.desc(), WorkSchedule.day_of_week
    ).paginate(page=page, per_page=20, error_out=False)

    return render_template('employees/schedules.html', employee=employee, schedules=schedules)

@bp.route('/<int:id>/schedules/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new_schedule(id):
    """Créer un nouveau planning pour un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = WorkScheduleForm()

    if form.validate_on_submit():
        schedule = WorkSchedule(
            employee_id=employee.id,
            owner_id=current_user.get_owner_id,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            day_of_week=form.day_of_week.data,
            shift_type=form.shift_type.data,
            start_time=form.start_time.data,
            end_time=form.end_time.data,
            break_duration=form.break_duration.data,
            notes=form.notes.data,
            is_recurring=form.is_recurring.data,
            created_by_id=current_user.id
        )

        db.session.add(schedule)
        db.session.commit()

        flash('Planning créé avec succès!', 'success')
        return redirect(url_for('employees.schedules', id=employee.id))

    return render_template('employees/schedule_form.html', form=form, employee=employee, title="Nouveau Planning")

@bp.route('/schedules/<int:schedule_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit_schedule(schedule_id):
    """Modifier un planning"""
    schedule = WorkSchedule.query.get_or_404(schedule_id)
    if schedule.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = WorkScheduleForm(obj=schedule)

    if form.validate_on_submit():
        form.populate_obj(schedule)
        db.session.commit()

        flash('Planning mis à jour avec succès!', 'success')
        return redirect(url_for('employees.schedules', id=schedule.employee_id))

    return render_template('employees/schedule_form.html',
                         form=form,
                         employee=schedule.employee,
                         schedule=schedule,
                         title="Modifier Planning")

@bp.route('/schedules/<int:schedule_id>/delete', methods=['POST'])
@login_required
@permission_required('can_manage_employees')
def delete_schedule(schedule_id):
    """Supprimer un planning"""
    schedule = WorkSchedule.query.get_or_404(schedule_id)
    if schedule.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    employee_id = schedule.employee_id
    db.session.delete(schedule)
    db.session.commit()

    flash('Planning supprimé avec succès!', 'success')
    return redirect(url_for('employees.schedules', id=employee_id))

# Routes pour la gestion des présences
@bp.route('/<int:id>/attendance')
@login_required
@permission_required('can_manage_employees')
def attendance(id):
    """Liste des présences d'un employé"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    page = request.args.get('page', 1, type=int)
    month = request.args.get('month', date.today().month, type=int)
    year = request.args.get('year', date.today().year, type=int)

    # Filtrer par mois/année
    start_date = date(year, month, 1)
    if month == 12:
        end_date = date(year + 1, 1, 1)
    else:
        end_date = date(year, month + 1, 1)

    attendances = Attendance.query.filter(
        Attendance.employee_id == id,
        Attendance.date >= start_date,
        Attendance.date < end_date
    ).order_by(Attendance.date.desc()).paginate(
        page=page, per_page=31, error_out=False)

    return render_template('employees/attendance.html',
                         employee=employee,
                         attendances=attendances,
                         current_month=month,
                         current_year=year)

@bp.route('/<int:id>/attendance/new', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def new_attendance(id):
    """Enregistrer une nouvelle présence"""
    employee = Employee.query.get_or_404(id)
    if employee.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = AttendanceForm()

    if form.validate_on_submit():
        attendance = Attendance(
            employee_id=employee.id,
            owner_id=current_user.get_owner_id,
            date=form.date.data,
            status=form.status.data,
            clock_in_time=datetime.combine(form.date.data, form.clock_in_time.data) if form.clock_in_time.data else None,
            clock_out_time=datetime.combine(form.date.data, form.clock_out_time.data) if form.clock_out_time.data else None,
            break_start_time=datetime.combine(form.date.data, form.break_start_time.data) if form.break_start_time.data else None,
            break_end_time=datetime.combine(form.date.data, form.break_end_time.data) if form.break_end_time.data else None,
            break_duration=form.break_duration.data,
            scheduled_hours=form.scheduled_hours.data,
            notes=form.notes.data
        )

        # Calculer automatiquement les heures travaillées
        attendance.calculate_hours_worked()

        db.session.add(attendance)
        db.session.commit()

        flash('Présence enregistrée avec succès!', 'success')
        return redirect(url_for('employees.attendance', id=employee.id))

    return render_template('employees/attendance_form.html', form=form, employee=employee, title="Nouvelle Présence")

@bp.route('/attendance/<int:attendance_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('can_manage_employees')
def edit_attendance(attendance_id):
    """Modifier une présence"""
    attendance = Attendance.query.get_or_404(attendance_id)
    if attendance.owner_id != current_user.get_owner_id:
        flash('Accès non autorisé', 'error')
        return redirect(url_for('employees.index'))

    form = AttendanceForm(obj=attendance)

    # Convertir les datetime en time pour le formulaire
    if attendance.clock_in_time:
        form.clock_in_time.data = attendance.clock_in_time.time()
    if attendance.clock_out_time:
        form.clock_out_time.data = attendance.clock_out_time.time()
    if attendance.break_start_time:
        form.break_start_time.data = attendance.break_start_time.time()
    if attendance.break_end_time:
        form.break_end_time.data = attendance.break_end_time.time()

    if form.validate_on_submit():
        attendance.date = form.date.data
        attendance.status = form.status.data
        attendance.clock_in_time = datetime.combine(form.date.data, form.clock_in_time.data) if form.clock_in_time.data else None
        attendance.clock_out_time = datetime.combine(form.date.data, form.clock_out_time.data) if form.clock_out_time.data else None
        attendance.break_start_time = datetime.combine(form.date.data, form.break_start_time.data) if form.break_start_time.data else None
        attendance.break_end_time = datetime.combine(form.date.data, form.break_end_time.data) if form.break_end_time.data else None
        attendance.break_duration = form.break_duration.data
        attendance.scheduled_hours = form.scheduled_hours.data
        attendance.notes = form.notes.data

        # Recalculer les heures travaillées
        attendance.calculate_hours_worked()

        db.session.commit()

        flash('Présence mise à jour avec succès!', 'success')
        return redirect(url_for('employees.attendance', id=attendance.employee_id))

    return render_template('employees/attendance_form.html',
                         form=form,
                         employee=attendance.employee,
                         attendance=attendance,
                         title="Modifier Présence")

# Routes API pour la sidebar et actions rapides
@bp.route('/api/stats')
@login_required
@permission_required('can_manage_employees')
def api_stats():
    """API pour les statistiques de la sidebar"""
    owner_id = current_user.get_owner_id

    total_employees = Employee.query.filter_by(owner_id=owner_id, status='ACTIVE').count()

    # Présents aujourd'hui
    today = date.today()
    today_present = Attendance.query.join(Employee).filter(
        Employee.owner_id == owner_id,
        Attendance.date == today,
        Attendance.status == AttendanceStatus.PRESENT
    ).count()

    return jsonify({
        'total_employees': total_employees,
        'today_present': today_present
    })

@bp.route('/api/active-employees')
@login_required
@permission_required('can_manage_employees')
def api_active_employees():
    """API pour la liste des employés actifs"""
    owner_id = current_user.get_owner_id

    employees = Employee.query.filter_by(
        owner_id=owner_id,
        status='ACTIVE'
    ).order_by(Employee.first_name, Employee.last_name).all()

    return jsonify([{
        'id': emp.id,
        'full_name': emp.full_name,
        'employee_id': emp.employee_id
    } for emp in employees])

# Importer les routes supplémentaires
from app.modules.employees import routes_payroll, routes_performance
