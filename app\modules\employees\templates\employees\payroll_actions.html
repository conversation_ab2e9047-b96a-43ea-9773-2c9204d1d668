{% extends "employees/base_hr.html" %}

{% block title %}Actions de Paie{% endblock %}

{% block hr_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-calculator me-2"></i>Actions de Paie
        <small class="text-muted">{{ current_month.strftime('%B %Y')|title }}</small>
    </h1>
    <a href="{{ url_for('employees.payroll_reports') }}" class="btn btn-outline-secondary">
        <i class="fas fa-chart-bar me-1"></i>Voir les Rapports
    </a>
</div>

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                <h5><PERSON><PERSON>er Fiches de Paie</h5>
                <p class="text-muted">Générer automatiquement les fiches de paie pour tous les employés actifs</p>
                <button class="btn btn-primary" onclick="generateAllPayrolls()">
                    <i class="fas fa-magic me-1"></i>Générer Tout
                </button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calculator fa-3x text-success mb-3"></i>
                <h5>Calculer Heures</h5>
                <p class="text-muted">Calculer automatiquement les heures travaillées basées sur les présences</p>
                <button class="btn btn-success" onclick="calculateHours()">
                    <i class="fas fa-clock me-1"></i>Calculer
                </button>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-3x text-info mb-3"></i>
                <h5>Traiter Paies</h5>
                <p class="text-muted">Marquer toutes les fiches de paie comme traitées et finalisées</p>
                <button class="btn btn-info" onclick="processAllPayrolls()">
                    <i class="fas fa-stamp me-1"></i>Traiter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Employés sans fiche de paie -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2 text-warning"></i>Employés sans Fiche de Paie ce Mois
        </h5>
    </div>
    <div class="card-body p-0">
        {% if employees_without_payroll %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Employé</th>
                        <th>Poste</th>
                        <th>Salaire de Base</th>
                        <th>Taux Horaire</th>
                        <th>Dernière Paie</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees_without_payroll %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ employee.first_name[0] }}{{ employee.last_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {{ employee.position }}<br>
                            <small class="text-muted">{{ employee.department or '-' }}</small>
                        </td>
                        <td>
                            <strong>{{ "%.2f"|format(employee.base_salary or 0) }}€</strong><br>
                            <small class="text-muted">{{ employee.payment_frequency.replace('_', ' ').title() }}</small>
                        </td>
                        <td>
                            {% if employee.hourly_rate %}
                                <strong>{{ "%.2f"|format(employee.hourly_rate) }}€/h</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set last_payroll = employee.payrolls.order_by(employee.payrolls.property.mapper.class_.pay_date.desc()).first() %}
                            {% if last_payroll %}
                                {{ last_payroll.pay_date.strftime('%d/%m/%Y') }}<br>
                                <small class="text-muted">{{ "%.2f"|format(last_payroll.net_pay or 0) }}€</small>
                            {% else %}
                                <span class="text-muted">Jamais</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('employees.new_payroll', id=employee.id) }}" 
                                   class="btn btn-sm btn-primary" title="Créer Fiche de Paie">
                                    <i class="fas fa-plus"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-success" 
                                        onclick="generatePayroll({{ employee.id }})" title="Générer Auto">
                                    <i class="fas fa-magic"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <h5 class="text-success">Toutes les fiches de paie sont à jour !</h5>
            <p class="text-muted">Tous les employés actifs ont une fiche de paie pour ce mois.</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script>
function generateAllPayrolls() {
    if (confirm('Êtes-vous sûr de vouloir générer toutes les fiches de paie manquantes ?')) {
        fetch('/employees/payroll/generate-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`${data.count} fiches de paie générées avec succès !`);
                location.reload();
            } else {
                alert('Erreur lors de la génération : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur : ' + error.message);
        });
    }
}

function calculateHours() {
    if (confirm('Calculer les heures travaillées pour tous les employés ce mois ?')) {
        fetch('/employees/attendance/calculate-hours', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Heures calculées pour ${data.count} employés !`);
            } else {
                alert('Erreur lors du calcul : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur : ' + error.message);
        });
    }
}

function processAllPayrolls() {
    if (confirm('Marquer toutes les fiches de paie comme traitées ?')) {
        fetch('/employees/payroll/process-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`${data.count} fiches de paie traitées !`);
                location.reload();
            } else {
                alert('Erreur lors du traitement : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur : ' + error.message);
        });
    }
}

function generatePayroll(employeeId) {
    if (confirm('Générer automatiquement la fiche de paie pour cet employé ?')) {
        fetch(`/employees/${employeeId}/payroll/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Fiche de paie générée avec succès !');
                location.reload();
            } else {
                alert('Erreur lors de la génération : ' + data.message);
            }
        })
        .catch(error => {
            alert('Erreur : ' + error.message);
        });
    }
}
</script>
{% endblock %}
